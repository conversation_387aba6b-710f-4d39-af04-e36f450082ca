<template>
	<view style="transition: all 0.3s;justify-content: center;position: relative;" class="w-full flex-1 flex flex-col">
		<!-- <image ref="beforeimgRef" :src="props.peopleImg" mode="widthFix" style="width: 100%;" /> -->
		<image
			class="regionalImg"
			:src="regionalImg" 
			mode="widthFix"
			style="position: absolute;left: 50%;top: 50%;transform: translate(-50%,-50%);width: 100%;z-index: 9;" 
		/>
		<!-- 底部导航栏2 -->
		<view style="z-index: 5;"
			class="fixed bottom-0 left-0 w-full bg-[#fff] border-t border-[#f0f0f0] transition-transform duration-300 transform ios-bottom-safe">
			<view class="flex justify-around items-center h-16 px-4">
				<view v-for="item in menus" style="font-size: 28rpx;border-color: #F39196;" class="font-semi" @click="chooseItem(item)">{{ item.label }}
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import skin_analysis from '@/static/skin_analysis.json'
console.log(skin_analysis, 'sdf');

import { ref } from "vue"
const props = defineProps({
	peopleImg: {
		type: String,
		default: ''
	}
});
let menus = ref([
	{
		label: "油光图",
		icon: "",
		act_icon: "",
		faceMap :"texture_enhanced_oily_area"
	},
	{
		label: "水分图",
		icon: "",
		act_icon: "",
		faceMap:"water_area"
	},
	{
		label: "毛孔图",
		icon: "",
		act_icon: "",
		faceMap:"rough_area"
	},
	{
		label: "红区图",
		icon: "",
		act_icon: "",
		faceMap:"red_area"
	},
	{
		label: "色沉图",
		icon: "",
		act_icon: "",
		faceMap:"brown_area"
	},
	
])
let regional = ref('')
let regionalImg = ref('')
function chooseItem(item){
	regional.value = item.faceMap
	regionalImg.value = skin_analysis.result.face_maps[regional]
	console.log(regionalImg.);
	
}
</script>

<style lang="scss" scoped>
.footer {
	height: 200;
}
</style>